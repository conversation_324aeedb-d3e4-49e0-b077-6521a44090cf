_wandb:
    value:
        cli_version: 0.20.1
        m: []
        python_version: 3.10.0
        t:
            "1":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 71
                - 98
                - 105
            "2":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 71
                - 98
                - 105
            "3":
                - 13
                - 15
                - 16
                - 55
            "4": 3.10.0
            "5": 0.20.1
            "6": 4.45.2
            "12": 0.20.1
            "13": linux-x86_64
dataset_size:
    value: 817
is_lora:
    value: true
lora_config:
    value:
        alpha: 32
        bias: none
        dropout: 0.1
        enabled: true
        r: 16
        target_modules:
            - q_proj
            - k_proj
            - v_proj
            - o_proj
            - gate_proj
            - up_proj
            - down_proj
model_name:
    value: Qwen/Qwen2.5-7B-Instruct
training_config:
    value:
        dataloader_pin_memory: true
        eval_steps: 100
        eval_strategy: steps
        fp16: true
        gradient_accumulation_steps: 4
        gradient_checkpointing: true
        greater_is_better: false
        learning_rate: "2e-4"
        load_best_model_at_end: true
        logging_steps: 10
        lr_scheduler_type: cosine
        metric_for_best_model: eval_loss
        num_train_epochs: 3
        optimizer: adamw_torch
        per_device_eval_batch_size: 4
        per_device_train_batch_size: 4
        remove_unused_columns: false
        report_to:
            - wandb
        save_steps: 500
        save_strategy: steps
        save_total_limit: 2
        warmup_ratio: 0.1
        weight_decay: 0.01
training_type:
    value: lora
