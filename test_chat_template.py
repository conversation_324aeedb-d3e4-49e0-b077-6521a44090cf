#!/usr/bin/env python3
"""
Test script to verify that the chat template fix works correctly.
"""

import os
import sys
from transformers import AutoTokenizer

def test_chat_template(model_path):
    """Test if the chat template is loaded correctly."""
    print(f"Testing chat template for model: {model_path}")
    
    try:
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        
        # Check if chat template exists initially
        has_template_initially = hasattr(tokenizer, 'chat_template') and tokenizer.chat_template is not None
        print(f"Has chat template initially: {has_template_initially}")
        
        if not has_template_initially:
            # Try to load from separate file
            chat_template_path = os.path.join(model_path, 'chat_template.jinja')
            if os.path.exists(chat_template_path):
                print(f"Loading chat template from {chat_template_path}")
                with open(chat_template_path, 'r', encoding='utf-8') as f:
                    tokenizer.chat_template = f.read()
                print("✅ Chat template loaded from separate file")
            else:
                print("❌ No chat template file found")
                return False
        
        # Test the template with a simple message
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello!"}
        ]
        
        try:
            # Test without think_mode
            result1 = tokenizer.apply_chat_template(
                messages, 
                tokenize=False, 
                add_generation_prompt=True
            )
            print("✅ Chat template works without think_mode")
            print("Sample output (first 200 chars):")
            print(result1[:200] + "..." if len(result1) > 200 else result1)
            
            # Test with think_mode=True
            result2 = tokenizer.apply_chat_template(
                messages, 
                tokenize=False, 
                add_generation_prompt=True,
                think_mode=True
            )
            print("✅ Chat template works with think_mode=True")
            print("Sample output (first 200 chars):")
            print(result2[:200] + "..." if len(result2) > 200 else result2)
            
            return True
            
        except Exception as e:
            print(f"❌ Error applying chat template: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error loading tokenizer: {e}")
        return False

def main():
    if len(sys.argv) != 2:
        print("Usage: python test_chat_template.py <model_path>")
        print("Example: python test_chat_template.py outputs/limo-old/code_switched-817-lora-merged")
        sys.exit(1)
    
    model_path = sys.argv[1]
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        sys.exit(1)
    
    success = test_chat_template(model_path)
    
    if success:
        print("\n🎉 Chat template test passed! The fix should work.")
    else:
        print("\n💥 Chat template test failed. There may still be issues.")
        sys.exit(1)

if __name__ == "__main__":
    main()
