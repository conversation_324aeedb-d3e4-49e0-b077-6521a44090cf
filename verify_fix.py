#!/usr/bin/env python3
"""
Simple verification script to check if the chat template fix works.
"""

import os
import json

def check_tokenizer_config(model_path):
    """Check if tokenizer config has chat template."""
    config_path = os.path.join(model_path, 'tokenizer_config.json')
    
    if not os.path.exists(config_path):
        print(f"❌ tokenizer_config.json not found in {model_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        has_chat_template = 'chat_template' in config and config['chat_template'] is not None
        print(f"Has chat_template in config: {has_chat_template}")
        
        if has_chat_template:
            template = config['chat_template']
            print(f"Template length: {len(template)} characters")
            print("✅ Chat template found in tokenizer config")
            return True
        else:
            # Check for separate chat template file
            chat_template_path = os.path.join(model_path, 'chat_template.jinja')
            if os.path.exists(chat_template_path):
                with open(chat_template_path, 'r', encoding='utf-8') as f:
                    template = f.read()
                print(f"✅ Chat template found in separate file: {chat_template_path}")
                print(f"Template length: {len(template)} characters")
                return True
            else:
                print("❌ No chat template found in config or separate file")
                return False
                
    except Exception as e:
        print(f"❌ Error reading tokenizer config: {e}")
        return False

def main():
    print("🔍 Verifying chat template fix...")
    
    # Test paths
    test_paths = [
        "outputs/limo/code_switched-817-lora",  # LoRA model (should have template in config)
        "outputs/limo-old/code_switched-817-lora-merged",  # Merged model (should have separate file)
        "outputs/limo/original-817-lora",  # Another LoRA model
    ]
    
    for path in test_paths:
        print(f"\n📁 Testing: {path}")
        if os.path.exists(path):
            success = check_tokenizer_config(path)
            if success:
                print(f"✅ {path} - Chat template available")
            else:
                print(f"❌ {path} - Chat template missing")
        else:
            print(f"⚠️  {path} - Path does not exist")
    
    print("\n🎯 Summary:")
    print("The fix in scripts/chat.py should handle both cases:")
    print("1. Models with chat_template in tokenizer_config.json")
    print("2. Models with separate chat_template.jinja file")
    print("3. Fallback to default template if neither exists")

if __name__ == "__main__":
    main()
